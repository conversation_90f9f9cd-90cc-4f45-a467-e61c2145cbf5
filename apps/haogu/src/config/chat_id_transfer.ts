import { PrismaClient as PrismaConfigClient } from 'model/prisma_client'
import { HaoguChatIdTransfer } from 'service/visualized_sop/common_sender/haogu'
import { PrismaClient } from '../../prisma_client'
import { getBotId } from 'config/chat_id'
import { manifest } from './manifest'
import logger from 'model/logger/logger'

class ChatIdTransfer extends HaoguChatIdTransfer {
  private prismaClient:PrismaClient
  private prismaConfigClient:PrismaConfigClient

  constructor(prismaClient:PrismaClient, prismaConfigClient:PrismaConfigClient) {
    super()
    this.prismaClient = prismaClient
    this.prismaConfigClient = prismaConfigClient
  }
  async getConversationIdAndToolUserId(chatId: string): Promise<{ toolUserId: string; conversationId: string; } | null> {
    const chatInfo = await this.prismaClient.chat.findFirst({ where:{ id:chatId } })
    if (!chatInfo) {
      logger.error(`转换chat_id时在chat表中搜索不到，chat_id:${chatId}`)
      return null
    }
    const botId = getBotId(chatId)
    const botConfig = await this.prismaConfigClient.config.findFirst({ where:{
      enterpriseName:manifest.projectName,
      wechatId:botId
    } })
    if (!botConfig?.enterpriseConfig?.['tool_user_id']) {
      logger.error(`转换chat_id时在config表中搜索不到，chat_id:${chatId},botId:${botId},enterpriseName:${manifest.projectName}`)
      return null
    }
    return {
      conversationId:chatInfo?.conversation_id,
      toolUserId:botConfig.enterpriseConfig['tool_user_id']
    }
  }

}